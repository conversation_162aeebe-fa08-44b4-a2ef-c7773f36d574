import {
  Injectable,
  NotFoundException,
  InternalServerErrorException,
  Logger,
  OnModuleInit,
  OnModuleDestroy,
} from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import { PuppeteerService } from '../../../utils/puppeteer.service';

@Injectable()
export class BillsService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(BillsService.name);
  private htmlTemplate: string | null = null;

  constructor(private readonly puppeteerService: PuppeteerService) {}

  async onModuleInit() {
    // Pre-load the HTML template to avoid file system operations on each request
    this.loadTemplate();
    // Initialize browser in PuppeteerService
    await this.puppeteerService.initBrowser();
  }

  async onModuleDestroy() {
    // PuppeteerService handles browser cleanup
  }

  private loadTemplate(): void {
    const candidatePaths = [
      path.resolve(__dirname, 'templates', 'billOfLading.html'),
      path.resolve(
        process.cwd(),
        'src',
        'business',
        'order',
        'bills',
        'templates',
        'billOfLading.html',
      ),
    ];

    const templatePath = candidatePaths.find((p) => fs.existsSync(p));
    if (!templatePath) {
      this.logger.error(
        `Bill template not found. Tried: ${candidatePaths.join(' , ')}`,
      );
      throw new NotFoundException('Bill template not found');
    }

    this.htmlTemplate = fs.readFileSync(templatePath, 'utf8');
    this.logger.log(`Bill template loaded from: ${templatePath}`);
  }

  async getBill(): Promise<Buffer> {
    try {
      if (!this.htmlTemplate) {
        throw new InternalServerErrorException('Bill template not loaded');
      }

      // Use the pre-loaded template
      const html = this.htmlTemplate;

      // Here you can replace placeholders dynamically if needed
      // html = html.replace('{{ORDER_NUMBER}}', 'TRK-1234-5678');

      // Now use the PuppeteerService (reusing the browser)
      const pdfBuffer = await this.puppeteerService.generatePDFFromHTML(html);
      return pdfBuffer;
    } catch (error: any) {
      this.logger.error('Failed to generate bill PDF', error?.stack || error);
      throw new InternalServerErrorException('Failed to generate bill PDF');
    }
  }
}
