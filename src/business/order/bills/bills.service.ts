import {
  Injectable,
  InternalServerErrorEx<PERSON>,
  Logger,
  OnModuleInit,
  OnModuleDestroy,
} from '@nestjs/common';
import { PuppeteerService } from '../../../utils/puppeteer.service';
import { OrderBills } from './bills.controller';
import {
  generateBillOfLadingHTML,
  BillOfLadingData,
} from './templates/billOfLading';

@Injectable()
export class BillsService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(BillsService.name);

  constructor(private readonly puppeteerService: PuppeteerService) {}

  async onModuleInit() {
    // Initialize browser in PuppeteerService
    await this.puppeteerService.initBrowser();
    this.logger.log('BillsService initialized with TypeScript templates');
  }

  async onModuleDestroy() {
    // PuppeteerService handles browser cleanup
  }

  async getBill(
    billName: OrderBills = OrderBills.BILL_OF_LADING,
    data: BillOfLadingData = {},
  ): Promise<Buffer> {
    try {
      let html: string;

      // Generate HTML based on the bill type
      switch (billName) {
        case OrderBills.BILL_OF_LADING:
          html = generateBillOfLadingHTML(data);
          break;
        case OrderBills.WAY_BILL:
          // TODO: Implement way bill template
          html = generateBillOfLadingHTML(data); // Fallback for now
          break;
        case OrderBills.SHIPPING_LABEL:
          // TODO: Implement shipping label template
          html = generateBillOfLadingHTML(data); // Fallback for now
          break;
        default:
          html = generateBillOfLadingHTML(data);
      }

      // Generate PDF using PuppeteerService
      const pdfBuffer = await this.puppeteerService.generatePDFFromHTML(html);
      return pdfBuffer;
    } catch (error: any) {
      this.logger.error('Failed to generate bill PDF', error?.stack || error);
      throw new InternalServerErrorException('Failed to generate bill PDF');
    }
  }

  // Convenience method for backward compatibility
  async getBillOfLading(data: BillOfLadingData = {}): Promise<Buffer> {
    return this.getBill(OrderBills.BILL_OF_LADING, data);
  }
}
