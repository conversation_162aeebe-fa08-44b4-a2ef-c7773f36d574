import {
  Injectable,
  NotFoundException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import { PuppeteerService } from '../../../utils/puppeteer.service';

@Injectable()
export class BillsService {
  private readonly logger = new Logger(BillsService.name);

  constructor(private readonly puppeteerService: PuppeteerService) {}

  async getBill(): Promise<Buffer> {
    try {
      const candidatePaths = [
        path.resolve(__dirname, 'templates', 'billOfLading.html'),
        path.resolve(
          process.cwd(),
          'src',
          'business',
          'order',
          'bills',
          'templates',
          'billOfLading.html',
        ),
      ];

      const templatePath = candidatePaths.find((p) => fs.existsSync(p));
      if (!templatePath) {
        this.logger.error(
          `Bill template not found. Tried: ${candidatePaths.join(' , ')}`,
        );
        throw new NotFoundException('Bill template not found');
      }

      const html = fs.readFileSync(templatePath, 'utf8');

      // Here you can replace placeholders dynamically if needed
      // html = html.replace('{{ORDER_NUMBER}}', 'TRK-1234-5678');

      // Now use the PuppeteerService (reusing the browser)
      const pdfBuffer = await this.puppeteerService.generatePDFFromHTML(html);
      return pdfBuffer;
    } catch (error: any) {
      this.logger.error('Failed to generate bill PDF', error?.stack || error);
      throw new InternalServerErrorException('Failed to generate bill PDF');
    }
  }
}
