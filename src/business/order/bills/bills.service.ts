import {
  Injectable,
  InternalServerErrorException,
  Logger,
  OnModuleInit,
  OnModuleDestroy,
} from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import { PuppeteerService } from '../../../utils/puppeteer.service';
import { OrderBills } from './bills.controller';

@Injectable()
export class BillsService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(BillsService.name);
  private templates: Map<string, string> = new Map();

  constructor(private readonly puppeteerService: PuppeteerService) {}

  async onModuleInit() {
    // Pre-load the HTML template to avoid file system operations on each request
    this.loadAllTemplates();
    // Initialize browser in PuppeteerService
    await this.puppeteerService.initBrowser();
  }

  async onModuleDestroy() {
    // PuppeteerService handles browser cleanup
  }

  private loadAllTemplates(): void {
    const templateNames: OrderBills[] = [
      OrderBills.BILL_OF_LADING,
      OrderBills.WAY_BILL,
      OrderBills.SHIPPING_LABEL,
    ];
    const basePaths = [
      path.resolve(__dirname, 'templates'),
      path.resolve(
        process.cwd(),
        'src',
        'business',
        'order',
        'bills',
        'templates',
      ),
    ];

    templateNames.forEach((name) => {
      const fileName = `${name}.html`;
      const templatePath = basePaths
        .map((base) => path.join(base, fileName))
        .find((p) => fs.existsSync(p));

      if (templatePath) {
        const html = fs.readFileSync(templatePath, 'utf8');
        this.templates.set(name, html);
        this.logger.log(`Loaded template: ${name} from ${templatePath}`);
      } else {
        this.logger.error(`Template not found: ${fileName}`);
      }
    });
  }

  public getTemplate(name: string): string {
    const template = this.templates.get(name);
    if (!template) {
      throw new Error(`Template not loaded: ${name}`);
    }

    return template;
  }

  async getBill(billName: OrderBills): Promise<Buffer> {
    try {
      const htmlTemplate = this.templates.get(billName);
      if (!htmlTemplate) {
        throw new InternalServerErrorException('Bill template not loaded');
      }
      console.log({ billName, htmlTemplate });
      // Use the pre-loaded template
      const html = htmlTemplate;

      // Here you can replace placeholders dynamically if needed
      // html = html.replace('{{ORDER_NUMBER}}', 'TRK-1234-5678');

      // Now use the PuppeteerService (reusing the browser)
      const pdfBuffer = await this.puppeteerService.generatePDFFromHTML(html);
      return pdfBuffer;
    } catch (error: any) {
      this.logger.error('Failed to generate bill PDF', error?.stack || error);
      throw new InternalServerErrorException('Failed to generate bill PDF');
    }
  }
}
