import { <PERSON>, Get, UseGuards, <PERSON><PERSON>, <PERSON><PERSON>, Param } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiCookieAuth,
  ApiProduces,
} from '@nestjs/swagger';
import { BillsService } from './bills.service';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '@core/auth/guards/tenant-auth.guard';
import { Response } from 'express';

@ApiTags('Business - Order - Bills')
@ApiBearerAuth()
@ApiCookieAuth()
@Controller({
  path: '/bills',
  version: '1',
})
@UseGuards(JwtAuthGuard, TenantAuthGuard)
export class BillsController {
  constructor(private readonly billsService: BillsService) {}

  @Get(':billName')
  @ApiOperation({ summary: 'Generate and return bill PDF' })
  @ApiOkResponse({ description: 'PDF binary stream' })
  @Header('Content-Type', 'application/pdf')
  @Header('Content-Disposition', 'inline; filename="bill-of-lading.pdf"')
  @ApiProduces('application/pdf')
  async getBill(
    // @Param('orderId') orderId: string,
    @Param('billName') billName: OrderBills,
    @Res() res: Response,
  ): Promise<any> {
    const pdfBuffer = await this.billsService.getBill(billName);
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `inline; filename="${'dd'}.pdf"`,
      'Content-Length': pdfBuffer.length,
    });
    res.send(pdfBuffer);
  }
}

export enum OrderBills {
  WAY_BILL = 'wayBill',
  BILL_OF_LADING = 'billOfLading',
  SHIPPING_LABEL = 'shippingLabel',
}
