<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bill of Lading</title>

    <style>
      /* Inline CSS for faster rendering - no external dependencies */
      * {
        box-sizing: border-box;
      }
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          sans-serif;
        margin: 0;
        padding: 24px;
        background-color: white;
        color: #1a202c;
        font-size: 14px;
        max-width: 896px;
        margin: 0 auto;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      }
      .flex {
        display: flex;
      }
      .flex-col {
        flex-direction: column;
      }
      .flex-wrap {
        flex-wrap: wrap;
      }
      .items-start {
        align-items: flex-start;
      }
      .items-center {
        align-items: center;
      }
      .justify-between {
        justify-content: space-between;
      }
      .justify-start {
        justify-content: flex-start;
      }
      .gap-1 {
        gap: 4px;
      }
      .gap-2 {
        gap: 8px;
      }
      .gap-4 {
        gap: 16px;
      }
      .gap-6 {
        gap: 24px;
      }
      .grid {
        display: grid;
      }
      .grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
      }
      .mb-2 {
        margin-bottom: 8px;
      }
      .mb-4 {
        margin-bottom: 16px;
      }
      .mt-6 {
        margin-top: 24px;
      }
      .p-2 {
        padding: 8px;
      }
      .p-3 {
        padding: 12px;
      }
      .p-6 {
        padding: 24px;
      }
      .px-2 {
        padding-left: 8px;
        padding-right: 8px;
      }
      .px-4 {
        padding-left: 16px;
        padding-right: 16px;
      }
      .py-2 {
        padding-top: 8px;
        padding-bottom: 8px;
      }
      .pt-2 {
        padding-top: 8px;
      }
      .pt-4 {
        padding-top: 16px;
      }
      .pb-4 {
        padding-bottom: 16px;
      }
      .text-xs {
        font-size: 12px;
      }
      .text-sm {
        font-size: 14px;
      }
      .text-base {
        font-size: 16px;
      }
      .text-2xl {
        font-size: 24px;
      }
      .text-right {
        text-align: right;
      }
      .text-center {
        text-align: center;
      }
      .font-semibold {
        font-weight: 600;
      }
      .font-bold {
        font-weight: 700;
      }
      .border {
        border: 1px solid #1a202c;
      }
      .border-r {
        border-right: 1px solid #1a202c;
      }
      .border-b {
        border-bottom: 1px solid #1a202c;
      }
      .divide-y > * + * {
        border-top: 1px solid #1a202c;
      }
      .bg-primary-50 {
        background-color: #f9fafb;
      }
      .text-primary-900 {
        color: #1a202c;
      }
      .border-primary-900 {
        border-color: #1a202c;
      }
      .w-fit {
        width: fit-content;
      }
      .w-full {
        width: 100%;
      }
      .w-\[20\%\] {
        width: 20%;
      }
      .w-\[40\%\] {
        width: 40%;
      }
      .w-\[80\%\] {
        width: 80%;
      }
      .w-\[200px\] {
        width: 200px;
      }
      .h-4 {
        height: 16px;
      }
      .h-8 {
        height: 32px;
      }
      .h-\[70px\] {
        height: 70px;
      }
      .flex-1 {
        flex: 1 1 0%;
      }
      .min-w-max {
        min-width: max-content;
      }
      .space-y-2 > * + * {
        margin-top: 8px;
      }
    </style>
  </head>
  <body>
    <!-- Header -->
    <div class="flex justify-between items-start mb-4">
      <div>
        <img
          src="https://via.placeholder.com/150x40?text=Lumigo+Logo"
          alt="Lumigo"
          class="h-8 mb-2"
        />
        <p class="text-xs">
          Lumigo Solution 2015 Inc (Lumigo Transport) <br />
          Phone: ************ Website: www.lumigotransport.ca
        </p>
      </div>
      <div class="text-right">
        <h2 class="text-2xl font-semibold">Bill of Lading</h2>
        <p class="text-xs">Submitted on: 11/08/2025, 02:00 PM</p>
      </div>
    </div>

    <!-- Tracking Number -->
    <div
      class="bg-primary-50 border border-primary-900 py-2 px-4 mb-4 text-center font-bold text-base"
    >
      Tracking number: #147795
    </div>

    <!-- Sender / Receiver -->
    <div class="grid grid-cols-2 border border-primary-900 mb-4">
      <div class="border-r border-primary-900">
        <div class="px-2 pt-2 pb-4 flex gap-2">
          <p class="font-semibold">Sender:</p>
          <p>
            EliteEdge Press,<br />
            7520 Chemin de la cote-de-Liesse<br />
            Saint-Laurent H4T 1E7
          </p>
        </div>
      </div>
      <div class="p-2 flex gap-2">
        <p class="font-semibold">Receiver:</p>
        <p>
          MAJESTIX, NOOR,<br />
          340 Rue Aime-Vincent 514-647-4350<br />
          Vaudreuil-Dorion J7V 5V5
        </p>
      </div>
    </div>

    <!-- Barcode Section -->
    <div
      class="flex items-center gap-4 border border-primary-900 p-3 mb-4 flex-wrap"
    >
      <div class="w-[40%]">
        <div class="flex flex-col justify-start items-center w-fit">
          <img
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAa0AAABV..."
            alt="Barcode"
            class="w-[200px] h-[70px]"
          />
        </div>
      </div>
      <div class="text-xs text-right flex gap-2 flex-col flex-1">
        <p class="flex flex-col items-start text-sm">
          <span class="font-semibold">Service Level:</span> Sameday
        </p>
        <div class="flex gap-4">
          <p class="text-sm">
            <span class="font-semibold">Collect at:</span> 12/08/2025, 10:00 AM
          </p>
          <p class="text-sm">
            <span class="font-semibold">Delivery at:</span> 12/08/2025, 10:00 AM
          </p>
        </div>
      </div>
    </div>

    <!-- Shipment Info Table -->
    <div class="border border-primary-900 divide-y divide-primary-900 mb-4">
      <div class="flex">
        <div class="w-[20%] p-2 font-bold border-r border-primary-900">
          Description:
        </div>
        <div class="w-[80%] p-2">1 Package + 1 Box = Total: 2</div>
      </div>
      <div class="flex">
        <div class="w-[20%] p-2 font-bold border-r border-primary-900">
          PO Number:
        </div>
        <div class="w-[80%] p-2"></div>
      </div>
      <div class="flex">
        <div class="w-[20%] p-2 font-bold border-r border-primary-900">
          Quantity:
        </div>
        <div class="w-[80%] p-2">2</div>
      </div>
      <div class="flex">
        <div class="w-[20%] p-2 font-bold border-r border-primary-900">
          Weight:
        </div>
        <div class="w-[80%] p-2">1</div>
      </div>
      <div class="flex">
        <div class="w-[20%] p-2 font-bold border-r border-primary-900">
          Dimensions:
        </div>
        <div class="w-[80%] p-2">0L x 0W x 0H</div>
      </div>
      <div class="flex">
        <div class="w-[20%] p-2 font-bold border-r border-primary-900">
          Department:
        </div>
        <div class="w-[80%] p-2"></div>
      </div>
      <div class="flex">
        <div class="w-[20%] p-2 font-bold border-r border-primary-900">
          Ref. Number:
        </div>
        <div class="w-[80%] p-2"></div>
      </div>
      <div class="flex">
        <div class="w-[20%] p-2 font-bold border-r border-primary-900">
          Declared Number:
        </div>
        <div class="w-[80%] p-2">$1500.00</div>
      </div>
      <div class="flex">
        <div class="w-[20%] p-2 font-bold border-r border-primary-900">
          Shipper:
        </div>
        <div class="w-[80%] p-2">
          EliteEdge Press, 7520 Chemin de liesse Saint Laurent, Quebec H4T 1E7
          Canada
        </div>
      </div>
    </div>

    <!-- Signature Section -->
    <div class="grid grid-cols-2 gap-6 text-xs">
      <div>
        <div
          class="bg-primary-50 px-2 py-2 w-fit font-bold border border-primary-900 text-sm"
        >
          Collection
        </div>
        <div class="flex flex-col gap-6 p-2 pt-4 space-y-2">
          <div class="flex gap-1 text-sm">
            <p class="font-bold min-w-max">Driver:</p>
            <div class="border-b border-primary-900 w-full h-4"></div>
          </div>
          <div class="flex gap-1 text-sm">
            <p class="font-bold min-w-max">Vehicle:</p>
            <div class="border-b border-primary-900 w-full h-4"></div>
          </div>
          <div class="flex gap-1 text-sm">
            <p class="font-bold min-w-max">Date & time:</p>
            <div class="border-b border-primary-900 w-full h-4"></div>
          </div>
          <div class="flex gap-1 text-sm">
            <p class="font-bold min-w-max">Received from:</p>
            <div class="border-b border-primary-900 w-full h-4"></div>
          </div>
          <div class="flex gap-1 mt-6">
            <p class="font-bold min-w-max text-sm">Signature:</p>
            <div class="border-b border-primary-900 w-full h-4"></div>
          </div>
        </div>
      </div>
      <div>
        <div
          class="bg-primary-50 px-2 py-2 w-fit font-bold border border-primary-900 text-sm"
        >
          Delivery
        </div>
        <div class="flex flex-col gap-6 p-2 pt-4 space-y-2">
          <div class="flex gap-1 text-sm">
            <p class="font-bold min-w-max">Driver:</p>
            <div class="border-b border-primary-900 w-full h-4"></div>
          </div>
          <div class="flex gap-1 text-sm">
            <p class="font-bold min-w-max">Vehicle:</p>
            <div class="border-b border-primary-900 w-full h-4"></div>
          </div>
          <div class="flex gap-1 text-sm">
            <p class="font-bold min-w-max">Date & time:</p>
            <div class="border-b border-primary-900 w-full h-4"></div>
          </div>
          <div class="flex gap-1 text-sm">
            <p class="font-bold min-w-max">Received from:</p>
            <div class="border-b border-primary-900 w-full h-4"></div>
          </div>
          <div class="flex gap-1 mt-6">
            <p class="font-bold min-w-max text-sm">Signature:</p>
            <div class="border-b border-primary-900 w-full h-4"></div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
