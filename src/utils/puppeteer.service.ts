import { Injectable, OnM<PERSON>ule<PERSON><PERSON>roy } from '@nestjs/common';
import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer';

@Injectable()
export class PuppeteerService implements OnModuleDestroy {
  private browser: Browser | null = null;

  async initBrowser(): Promise<Browser> {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=TranslateUI',
          '--disable-ipc-flooding-protection',
          '--disable-renderer-backgrounding',
          '--disable-backgrounding-occluded-windows',
          '--disable-background-timer-throttling',
          '--disable-background-networking',
          '--disable-default-apps',
          '--disable-extensions',
          '--disable-sync',
          '--metrics-recording-only',
          '--no-default-browser-check',
          '--no-pings',
          '--password-store=basic',
          '--use-mock-keychain',
        ],
      });
    }
    return this.browser;
  }

  async closeBrowser(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  async generatePDFFromHTML(
    htmlContent: string,
    options?: any,
  ): Promise<Buffer> {
    const browser = await this.initBrowser();
    const page = await browser.newPage();
    try {
      // Set viewport for consistent rendering
      await page.setViewport({ width: 1200, height: 800 });

      // Disable images and other resources for faster rendering if not needed
      await page.setRequestInterception(true);
      page.on('request', (req) => {
        const resourceType = req.resourceType();
        if (resourceType === 'image' && !req.url().startsWith('data:')) {
          // Allow data URLs (inline images) but block external images
          void req.abort();
        } else {
          void req.continue();
        }
      });

      // Use 'domcontentloaded' for faster rendering since we have inline CSS
      await page.setContent(htmlContent, {
        waitUntil: 'domcontentloaded',
        timeout: 8000, // Reduced timeout
      });

      // Wait a bit for fonts to render properly
      await new Promise((resolve) => setTimeout(resolve, 500));

      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: { top: '20px', right: '20px', bottom: '20px', left: '20px' },
        displayHeaderFooter: false,
        ...options,
      });
      return Buffer.from(pdfBuffer);
    } finally {
      await page.close();
    }
  }

  async onModuleDestroy() {
    await this.closeBrowser();
  }
}
